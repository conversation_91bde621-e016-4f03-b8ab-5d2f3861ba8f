<template>
    <div>
        <el-dialog
            :close-on-click-modal="false"
            :visible.sync="isShowGallery"
            width="90%"
            append-to-body
            custom-class="enhanced_gallery_dialog"
            v-loading="loading"
            :before-close="closeGallery"
        >
            <div class="gallery-container">
                <!-- 左侧画廊区域 -->
                <div class="gallery-left" ref="leftGallery" :class="{ 'with-sidebar': hasRightSidebarSlot && showRightSidebar }">
                    <!-- 关闭按钮 - 始终显示 -->
                    <button class="close-btn" @click="closeGallery">
                        <i class="el-icon-close"></i>
                    </button>

                    <!-- 主要查看区域 -->
                    <div class="gallery-top" id="gallery-box" @mousemove="showNavArrows" @mouseleave="hideNavArrows">
                        <!-- 图片/视频标题显示区域 -->
                        <div class="file-info-bar" :class="{ 'hidden': !showFileInfo }">
                            <div class="file-title">
                                <span v-if="currentFile.label" v-html="currentFile.label"></span>
                                <span v-else>{{ currentSliderIndex + 1 }} / {{ fileList.length }}</span>
                            </div>
                            <div class="file-actions">
                                <button class="action-btn" @click="toggleFileInfo">
                                    <i class="el-icon-info"></i>
                                </button>
                                <!-- 切换右侧插槽按钮 - 只有当有插槽内容时才显示 -->
                                <button v-if="hasRightSidebarSlot" class="action-btn" @click="toggleRightSidebar" :title="showRightSidebar ? '隐藏侧边栏' : '显示侧边栏'">
                                    <i :class="showRightSidebar ? 'el-icon-d-arrow-right' : 'el-icon-d-arrow-left'"></i>
                                </button>
                            </div>
                        </div>

                        <!-- 标签切换按钮 - 当标签隐藏时显示 -->
                        <button class="toggle-info-btn" @click="toggleFileInfo" v-if="!showFileInfo">
                            <i class="el-icon-info"></i>
                        </button>
                        <!-- 主要内容区域 -->
                        <div class="main-content-area">
                            <!-- 左右导航箭头 -->
                            <button
                                class="gallery-nav-button prev"
                                :class="{ 'visible': showArrows, 'disabled': currentSliderIndex <= 0 }"
                                @click="prevImage"
                                :disabled="currentSliderIndex <= 0"
                            >
                                <i class="el-icon-arrow-left"></i>
                            </button>

                            <button
                                class="gallery-nav-button next"
                                :class="{ 'visible': showArrows, 'disabled': currentSliderIndex >= fileList.length - 1 }"
                                @click="nextImage"
                                :disabled="currentSliderIndex >= fileList.length - 1"
                            >
                                <i class="el-icon-arrow-right"></i>
                            </button>

                            <!-- 图片显示 -->
                            <template v-if="currentFile.fileType === 'image'">
                                <div class="image-container"
                                     @wheel.prevent="handleImageWheel($event)"
                                     @mousedown="startImageDrag($event)"
                                     @mousemove="moveImage($event)"
                                     @mouseup="endImageDrag"
                                     @mouseleave="endImageDrag"
                                     @dblclick="handleDoubleClick">
                                    <img :src="currentFile.url"
                                         class="preview"
                                         draggable="false"
                                         @error="handleImageError(currentFile)"
                                         :style="imageTransformStyle"
                                         ref="previewImage"/>
                                    <button v-if="imageScale !== 1 || imageOffsetX !== 0 || imageOffsetY !== 0"
                                            class="reset-zoom-btn"
                                            @click="resetImageTransform">
                                        <i class="el-icon-refresh-right" style="transform: rotate(270deg);"></i>
                                    </button>
                                </div>
                            </template>

                            <!-- 视频显示 -->
                            <template v-else-if="currentFile.fileType === 'video'">
                                <div class="video-container">
                                    <!-- 非CEF环境下使用web播放器组件 -->
                                    <video-player
                                        v-if="!isCefEnvironment"
                                        ref="videoPlayer"
                                        :video-src="currentFile.url"
                                        :poster="currentFile.thumbnail"
                                        :id="'gallery-video-' + currentSliderIndex"
                                        @error="handleVideoError(currentSliderIndex)"
                                    ></video-player>
                                    <!-- CEF环境下显示占位图，实际由原生播放器显示 -->

                                </div>
                            </template>
                            <!-- PDF显示 - 在主区域只显示占位图 -->
                            <template v-else-if="currentFile.fileType === 'pdf'">

                                <div class="pdf-container">
                                    <img
                                        src="static/resource_pc/images/file_icon/pdf.png"
                                        class="preview"
                                        draggable="false"
                                    />
                                    <!-- PDF阅读器 -->
                                    <pdf-reader class="pdfReader" :url="currentFile.url"></pdf-reader>
                                </div>
                            </template>
                            <!-- DCM显示 - 在主区域只显示占位图 -->
                            <template v-else-if="currentFile.fileType === 'dcm'">
                                <div class="pdf-container">
                                    <img
                                        src="static/resource_pc/images/file_icon/dcm.png"
                                        class="preview"
                                        draggable="false"
                                    />
                                </div>
                            </template>
                        </div>
                    </div>

                    <!-- 缩略图区域 -->
                    <div class="thumb_wrap">
                        <div ref="thumb_scroll_wrap" class="thumb_scroll_wrap">
                            <vue-slide :key="slideKey" class="thumb_slide" ref="thumb_slide" :ops="ops">
                                <div @mousewheel.prevent.stop class="thumbnails-container">
                                    <div
                                        v-for="(file, index) in fileList"
                                        class="thumb_item"
                                        :class="{ current_thumb: index === currentSliderIndex }"
                                        @mousedown="mousedownThumb($event, index)"
                                        @mouseup="mouseupThumb($event, index)"
                                        :key="index + '_' + file.url"
                                        :data-file-type="file.fileType"
                                    >
                                        <!-- 图片缩略图 -->
                                        <template v-if="file.fileType === 'image'">
                                            <img :src="file.url" class="preview" draggable="false" @error="handleImageError(file)"/>
                                            <div class="file-type-indicator">
                                                <i class="el-icon-picture"></i>
                                            </div>
                                            <!-- 添加图片标签 -->
                                            <span v-if="file.imageType"
                                                class="image-tag"
                                                :class="'tag-' + file.imageType"
                                                :style="{ '--tag-bg-color': getTagColor(file.imageType) }">{{
                                                imageTagType[file.imageType] || file.imageType
                                            }}</span>
                                        </template>
                                        <!-- 视频缩略图 -->
                                        <template v-else-if="file.fileType === 'video'">
                                            <img :src="file.thumbnail || `static/resource_pc/images/file_icon/${getFileType(file.url)}.png`" class="preview" draggable="false"/>
                                            <div class="file-type-indicator video">
                                                <i class="el-icon-video-camera"></i>
                                            </div>
                                        </template>

                                        <!-- PDF缩略图 -->
                                        <template v-else-if="file.fileType === 'pdf'">
                                            <img src="static/resource_pc/images/file_icon/pdf.png" class="preview" draggable="false" />
                                            <div class="file-type-indicator pdf">
                                                <i class="el-icon-document"></i>
                                            </div>
                                        </template>

                                        <!-- DCM缩略图 -->
                                        <template v-else-if="file.fileType === 'dcm'">
                                            <img src="static/resource_pc/images/file_icon/dcm.png" class="preview" draggable="false" />
                                            <div class="file-type-indicator dcm">
                                                <i class="el-icon-picture"></i>
                                            </div>
                                        </template>
                                    </div>
                                </div>
                            </vue-slide>
                        </div>
                        <button class="nav-button prev" @click="lastPage">
                            <i class="el-icon-arrow-left"></i>
                        </button>
                        <button class="nav-button next" @click="nextPage">
                            <i class="el-icon-arrow-right"></i>
                        </button>
                    </div>
                </div>

                <!-- 右侧插槽区域 - 只有当有插槽内容时才显示 -->
                <div v-if="hasRightSidebarSlot" class="gallery-right" v-show="showRightSidebar" :style="{ width: rightSidebarWidth + 'px' }">
                    <div class="sidebar-header" v-if="defaultShowSidebarHeader">
                        <span class="sidebar-title">{{  }}</span>
                        <button class="sidebar-close-btn" @click="toggleRightSidebar">
                            <i class="el-icon-close"></i>
                        </button>
                    </div>
                    <div class="sidebar-content">
                        <!-- 插槽内容 -->
                        <slot name="rightSidebar" :currentFile="currentFile" :currentIndex="currentSliderIndex"></slot>
                    </div>
                </div>

            </div>
        </el-dialog>
    </div>
</template>
<script>
import vueSlide from "vuescroll";
import VideoPlayer from "./videoPlayer";
import Tool from "@/common/tool";
import { getRealtimeConsultationSize } from "../lib/common_realtimeVideo";
export default {
    name: "SimpleGallery",
    components: { vueSlide, VideoPlayer, pdfReader: () => import(/* webpackPrefetch: true */ '../components/pdfReader') },
    props: {
        loading: {
            type: Boolean,
            default: false
        },

        // 添加自定义标签类型映射
        imageTagTypeMap: {
            type: Object,
            default: () => ({})
        },
        // 添加自定义标签颜色映射
        imageTagColors: {
            type: Object,
            default: () => ({})
        },

        // 右侧插槽相关props
        defaultShowRightSidebar: {
            type: Boolean,
            default: false
        },
        defaultRightSidebarWidth: {
            type: Number,
            default: 300
        },
        defaultShowSidebarHeader: {
            type: Boolean,
            default: true
        },
        defaultSidebarTitle: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            getFileType: Tool.getFileType,
            isShowGallery: false,
            currentSliderIndex: -1,
            mousedownThumpPoint: null,
            fileList: [],
            currentFile: {},
            slideKey: 0,
            showFileInfo: true,
            showArrows: false,
            arrowsTimeout: null,
            ops: {
                vuescroll: {
                    mode: "slide",
                    sizeStrategy: "percent",
                    detectResize: true,
                    locking: true
                },
                scrollPanel: {
                    scrollingY: false
                }
            },
            imageScale: 1,
            imageOffsetX: 0,
            imageOffsetY: 0,
            isDragging: false,
            dragStartX: 0,
            dragStartY: 0,
            lastOffsetX: 0,
            lastOffsetY: 0,
            containerBounds: null,
            imageBounds: null,
            maxZoomScale: 3,
            doubleClickZoomScale: 2,

            // 防止重复显示视频错误消息
            lastVideoErrorTime: null,

            // 右侧插槽相关
            showRightSidebar: this.defaultShowRightSidebar,
            rightSidebarWidth: this.defaultRightSidebarWidth,
        };
    },
    computed: {
        imageTransformStyle() {
            return {
                transform: `scale(${this.imageScale}) translate(${this.imageOffsetX}px, ${this.imageOffsetY}px)`,
                transition: this.isDragging ? 'none' : 'transform 0.2s ease'
            };
        },

        // 修改为使用prop传入的标签类型映射
        imageTagType() {
            return this.imageTagTypeMap;
        },

        // 检测是否有右侧插槽内容
        hasRightSidebarSlot() {
            return !!(this.$slots.rightSidebar || this.$scopedSlots.rightSidebar);
        }
    },
    watch: {
        currentSliderIndex(newIndex) {
            if (newIndex >= 0 && this.fileList[newIndex]) {
                this.currentFile = this.fileList[newIndex];
                this.handleFileTypeChange();
                this.slideThumb(newIndex);
            }
            // Reset image transform when changing slides
            this.resetImageTransform();
        },
        fileList: {
            handler(newFileList) {
                // 如果文件列表为空，重置所有状态
                if (!newFileList || newFileList.length === 0) {
                    this.currentSliderIndex = -1;
                    this.currentFile = {};
                    return;
                }

                // 如果当前索引超出新列表范围，重置为第一张
                if (this.currentSliderIndex >= newFileList.length) {
                    this.currentSliderIndex = 0;
                }

                // 如果当前索引有效，更新当前文件
                if (this.currentSliderIndex >= 0) {
                    this.currentFile = newFileList[this.currentSliderIndex];
                    this.handleFileTypeChange();
                }

                // 强制更新 slideKey 以刷新缩略图
                this.slideKey = Date.now();


            },
            deep: true
        },

    },
    mounted() {
        // 检查是否为CEF环境
        this.isCefEnvironment = Tool.checkAppClient('Cef');

        document.addEventListener("visibilitychange", () => {
            if (document.hidden) {
                this.closeGallery();
            }
        });


    },
    beforeDestroy() {
        this.closeGallery();
        if (this.arrowsTimeout) {
            clearTimeout(this.arrowsTimeout);
        }
    },
    methods: {


        // 打开画廊
        openGallery(files, startIndex = 0, options = {}) {
            console.log(files);
            this.isShowGallery = true;
            this.slideKey = Date.now();

            // 预处理文件列表，确保每个文件都有 fileType
            let processedFiles = Array.isArray(files) ? [...files] : [];
            processedFiles = processedFiles.map(file => {
                // 如果文件没有 fileType，则根据 URL 后缀确定类型
                if (!file.fileType && file.url) {
                    const extension = Tool.getFileType(file.url);
                    // 根据扩展名设置文件类型
                    if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(extension)) {
                        file.fileType = 'image';
                    } else if (['mp4', 'webm', 'ogg', 'mov', 'avi', 'flv','m3u8'].includes(extension)) {
                        file.fileType = 'video';
                    } else if (extension === 'pdf') {
                        file.fileType = 'pdf';
                    } else if(extension === 'dcm'){
                        file.fileType = 'dcm';
                    }
                }
                return file;
            });

            this.fileList = processedFiles;
            this.showFileInfo = true;
            this.showArrows = false;

            // 重置右侧插槽状态 - 只有当有插槽内容时才设置
            if (this.hasRightSidebarSlot) {
                this.showRightSidebar = this.defaultShowRightSidebar;
                this.rightSidebarWidth = this.defaultRightSidebarWidth;
            } else {
                this.showRightSidebar = false;
            }

            // 应用可选配置 - 传入图片标签数据
            if (options.imageTagData) {
                this.processImageTagData(options.imageTagData);
            } else {
                // 初始化图片标签类型
                this.initImageType();
            }



            // 重置图片变换状态
            this.resetImageTransform();

            // 确保 startIndex 在有效范围内
            if (this.fileList.length > 0) {
                startIndex = Math.max(0, Math.min(startIndex, this.fileList.length - 1));
                this.$nextTick(() => {
                    this.setCurrentIndex(startIndex);
                });
            } else {
                this.currentSliderIndex = -1;
                this.currentFile = {};
            }
        },

        // 关闭画廊
        closeGallery() {
            this.resetImageTransform();
            this.isShowGallery = false;
            this.currentSliderIndex = -1;
            this.currentFile = {};
            // 关闭DCM查看器
            if (window.CWorkstationCommunicationMng && typeof window.CWorkstationCommunicationMng.CloseDrImage === 'function') {
                window.CWorkstationCommunicationMng.CloseDrImage({});
            }

            // 关闭原生视频播放器
            if (Tool.checkAppClient('Cef')) {
                window.CWorkstationCommunicationMng.exitUltrasoundDesktop({});
            }

            // 停止所有视频播放
            const videos = document.querySelectorAll("video");
            for (let video of videos) {
                video.pause();
            }
        },
        // 初始化DCM文件
        initDcmIfNeed() {
            if (this.currentFile.fileType === 'dcm') {
                // 生成DCM URL
                const dcm_url = this.currentFile.url;
                // 调用系统DCM查看器
                if (window.CWorkstationCommunicationMng && typeof window.CWorkstationCommunicationMng.DisplayDrImage === 'function') {
                    window.CWorkstationCommunicationMng.DisplayDrImage({
                        dcm_url: dcm_url,
                    });
                } else {
                    console.warn('CWorkstationCommunicationMng.DisplayDrImage not available');
                }
            } else {
                // 如果切换到非DCM文件，关闭DCM查看器
                if (window.CWorkstationCommunicationMng && typeof window.CWorkstationCommunicationMng.CloseDrImage === 'function') {
                    window.CWorkstationCommunicationMng.CloseDrImage({});
                }
            }
        },

        // 切换文件信息显示
        toggleFileInfo() {
            this.showFileInfo = !this.showFileInfo;
        },

        // 切换右侧插槽显示
        toggleRightSidebar() {
            if (this.hasRightSidebarSlot) {
                this.showRightSidebar = !this.showRightSidebar;
            }
        },

        // 设置当前索引
        setCurrentIndex(index) {
            if (index >= 0 && index < this.fileList.length) {
                this.resetImageTransform();
                this.currentSliderIndex = index;
            }
        },

        // 显示导航箭头
        showNavArrows() {
            this.showArrows = true;
            if (this.arrowsTimeout) {
                clearTimeout(this.arrowsTimeout);
            }
        },

        // 隐藏导航箭头
        hideNavArrows() {
            if (this.arrowsTimeout) {
                clearTimeout(this.arrowsTimeout);
            }
            this.arrowsTimeout = setTimeout(() => {
                this.showArrows = false;
            }, 1000);
        },

        // 上一张图片
        prevImage() {
            if (this.currentSliderIndex > 0) {
                this.setCurrentIndex(this.currentSliderIndex - 1);
            }
        },

        // 下一张图片
        nextImage() {
            if (this.currentSliderIndex < this.fileList.length - 1) {
                this.setCurrentIndex(this.currentSliderIndex + 1);
            }
        },

        // 处理文件类型变化
        handleFileTypeChange() {
            // 在下一个DOM更新周期处理视频播放
            this.$nextTick(() => {
                if (this.currentFile.fileType === 'video') {
                    // 检查是否为CEF环境
                    const isCef = Tool.checkAppClient('Cef');

                    if (isCef) {
                        // 在CEF环境下使用原生播放器
                        const container = document.querySelector('.video-container');
                        if (container) {
                            const domRect = getRealtimeConsultationSize(container);
                            let json = {
                                width:domRect.width,
                                height:domRect.height,
                                left:domRect.left,
                                top:domRect.top,
                                ultrasound_video:this.currentFile.url,
                                video_with_audio:1,
                                type:4
                            };
                            if(this.$store.state.systemConfig.serverInfo.network_environment === 1){
                                json.ultrasound_video = Tool.replaceInternalNetworkEnvImageHost(json.ultrasound_video)
                            }
                            window.CWorkstationCommunicationMng.switchVideo(json);
                        }
                    } else {
                        // 非CEF环境使用web播放器
                        if (this.$refs.videoPlayer) {
                            // 如果是数组，取第一个元素
                            const videoPlayerRef = Array.isArray(this.$refs.videoPlayer)
                                ? this.$refs.videoPlayer[0]
                                : this.$refs.videoPlayer;

                            if (videoPlayerRef && typeof videoPlayerRef.loadVideo === 'function') {
                                videoPlayerRef.loadVideo(this.currentFile.url);
                            }
                        }
                    }
                }else{
                    // 关闭原生视频播放器
                    if (Tool.checkAppClient('Cef')) {
                        window.CWorkstationCommunicationMng.exitUltrasoundDesktop({});
                    }

                    // 停止所有视频播放
                    const videos = document.querySelectorAll("video");
                    for (let video of videos) {
                        video.pause();
                    }

                }
            });
            this.initDcmIfNeed(this.currentFile);
        },

        // 缩略图滑动
        slideThumb(index) {
            let thumb_slide = this.$refs.thumb_slide;
            let thumb_scroll_wrap = this.$refs.thumb_scroll_wrap;
            if (!thumb_slide || !thumb_scroll_wrap) {
                return;
            }

            let scroll_width = thumb_scroll_wrap.clientWidth;
            let left = index * 157 - scroll_width / 2 + 78;
            thumb_slide && thumb_slide.scrollTo({ x: left }, 100);
        },

        // 缩略图鼠标按下
        mousedownThumb(event, index) {
            this.mousedownThumpPoint = {
                x: event.x,
                y: event.y
            };
        },

        // 缩略图鼠标释放
        mouseupThumb(event, index) {
            let offsetX = this.mousedownThumpPoint.x - event.x;
            let offsetY = this.mousedownThumpPoint.y - event.y;

            if (Math.abs(offsetX) < 20 && Math.abs(offsetY) < 20) {
                this.setCurrentIndex(index);
            }
        },

        // 上一页
        lastPage() {
            let thumb_slide = this.$refs.thumb_slide;
            if (!thumb_slide) {
                return;
            }

            let left = thumb_slide.getPosition().scrollLeft;
            let thumb_scroll_wrap = this.$refs.thumb_scroll_wrap;
            let scroll_width = thumb_scroll_wrap.clientWidth;
            left -= scroll_width;
            thumb_slide && thumb_slide.scrollTo({ x: left }, 150);
        },

        // 下一页
        nextPage() {
            let thumb_slide = this.$refs.thumb_slide;
            if (!thumb_slide) {
                return;
            }

            let left = thumb_slide.getPosition().scrollLeft;
            let thumb_scroll_wrap = this.$refs.thumb_scroll_wrap;
            let scroll_width = thumb_scroll_wrap.clientWidth;
            left += scroll_width;
            thumb_slide && thumb_slide.scrollTo({ x: left }, 150);
        },

        // 图片加载错误处理
        handleImageError(file) {
            console.error('Image load error:', file.url);
            // 可以设置一个默认的错误图片
            // file.url = 'path/to/error-image.jpg';
        },

        // 视频加载错误处理
        handleVideoError(index) {
            // 防止重复显示错误消息
            if (this.lastVideoErrorTime && Date.now() - this.lastVideoErrorTime < 1000) {
                return;
            }
            this.lastVideoErrorTime = Date.now();
            this.$message.error('Video load error');
        },

        // 初始化图片标签类型
        initImageType() {
            if (!this.fileList || !this.fileList.length) {
                return;
            }

            this.fileList.forEach(file => {
                // 如果文件有关联的 exam 数据，从中获取 imageType
                if (file.exam && file.exam.caseData && file.exam.caseData.ImageTag) {
                    file.imageType = file.exam.caseData.ImageTag[file.file_id];
                }
            });
        },

        // 处理图片标签数据
        processImageTagData(tagData) {
            if (!this.fileList || !this.fileList.length || !tagData) {
                return;
            }

            this.fileList.forEach(file => {
                if (file.file_id && tagData[file.file_id]) {
                    file.imageType = tagData[file.file_id];
                }
            });
        },

        // 获取标签颜色
        getTagColor(tagType) {
            // 默认颜色映射，可被props覆盖
            const defaultColors = {
                '1': '#0030c6', // 蓝色
                '2': '#f90012', // 红色
                '3': '#00c626'  // 绿色
            };

            // 如果props提供了颜色映射，使用props
            if (this.imageTagColors && this.imageTagColors[tagType]) {
                return this.imageTagColors[tagType];
            }

            // 否则使用默认颜色，如果没有，返回默认蓝色
            return defaultColors[tagType] || defaultColors['1'];
        },

        // 更新边界框计算方法
        updateBoundingBoxes() {
            const container = this.$el.querySelector('.image-container');
            const image = this.$el.querySelector('.image-container .preview');

            if (container && image) {
                this.containerBounds = container.getBoundingClientRect();

                // 获取图片的原始尺寸（未缩放状态）
                const naturalWidth = image.naturalWidth;
                const naturalHeight = image.naturalHeight;

                // 获取图片当前显示尺寸
                const rect = image.getBoundingClientRect();

                // 保存图片信息
                this.imageBounds = {
                    width: rect.width / this.imageScale,
                    height: rect.height / this.imageScale,
                    naturalWidth,
                    naturalHeight,
                    ratio: rect.width / rect.height
                };
            }
        },

        // 处理双击缩放
        handleDoubleClick(event) {
            if (this.currentFile.fileType !== 'image') {
                return;
            }

            // 如果已经放大，则重置为原始大小
            if (this.imageScale > 1) {
                this.resetImageTransform();
            } else {
                // 放大到预设比例
                this.imageScale = this.doubleClickZoomScale;

                // 计算容器和图片尺寸以限制拖动范围
                this.$nextTick(() => {
                    this.updateBoundingBoxes();
                });
            }
        },

        // 处理鼠标滚轮缩放
        handleImageWheel(event) {
            if (this.currentFile.fileType !== 'image') {
                return;
            }

            // 确定缩放方向
            const delta = event.deltaY || event.detail || event.wheelDelta;
            const zoomFactor = delta > 0 ? 0.9 : 1.1; // 缩小或放大

            // 计算新的缩放比例
            let newScale = this.imageScale * zoomFactor;

            // 限制缩放比例在0.5到maxZoomScale之间
            newScale = Math.max(0.5, Math.min(this.maxZoomScale, newScale));

            // 应用新的缩放比例
            this.imageScale = newScale;

            // 更新边界框以限制拖动范围
            this.$nextTick(() => {
                this.updateBoundingBoxes();
            });
        },

        // 开始图片拖动
        startImageDrag(event) {
            if (this.currentFile.fileType !== 'image' || this.imageScale <= 1) {
                return;
            }

            this.isDragging = true;
            this.dragStartX = event.clientX;
            this.dragStartY = event.clientY;
            this.lastOffsetX = this.imageOffsetX;
            this.lastOffsetY = this.imageOffsetY;

            // 更新边界框以限制拖动范围
            this.updateBoundingBoxes();

            // 改变光标样式
            event.target.style.cursor = 'grabbing';
        },

        // 拖动图片时限制在容器内
        moveImage(event) {
            if (!this.isDragging) {
                return;
            }

            const dx = (event.clientX - this.dragStartX) / this.imageScale;
            const dy = (event.clientY - this.dragStartY) / this.imageScale;

            // 计算新的偏移量
            let newOffsetX = this.lastOffsetX + dx;
            let newOffsetY = this.lastOffsetY + dy;

            // 如果有有效的边界框，则应用约束
            if (this.containerBounds && this.imageBounds) {
                // 计算图片的实际尺寸（考虑缩放）
                const scaledWidth = this.imageBounds.width;
                const scaledHeight = this.imageBounds.height;

                // 计算图片超出容器的部分（每边）
                const containerWidth = this.containerBounds.width;
                const containerHeight = this.containerBounds.height;

                // 计算最大可移动距离（考虑缩放）
                const maxMoveX = Math.max(0, (scaledWidth * this.imageScale - containerWidth) / (2 * this.imageScale));
                const maxMoveY = Math.max(0, (scaledHeight * this.imageScale - containerHeight) / (2 * this.imageScale));

                // 限制偏移量在允许范围内
                newOffsetX = Math.max(-maxMoveX, Math.min(maxMoveX, newOffsetX));
                newOffsetY = Math.max(-maxMoveY, Math.min(maxMoveY, newOffsetY));
            }

            // 应用受限制的偏移量
            this.imageOffsetX = newOffsetX;
            this.imageOffsetY = newOffsetY;
        },

        // End image drag
        endImageDrag(event) {
            if (!this.isDragging) {
                return;
            }

            this.isDragging = false;
            if (event && event.target) {
                event.target.style.cursor = 'grab';
            }
        },

        // Reset image to original size and position
        resetImageTransform() {
            this.imageScale = 1;
            this.imageOffsetX = 0;
            this.imageOffsetY = 0;
            this.isDragging = false;
            this.containerBounds = null;
            this.imageBounds = null;
        },
    }
};
</script>

<style lang="scss">
.enhanced_gallery_dialog {
    background: #0e0e0e;
    height: 90% !important;
    display: flex;
    flex-direction: column;
    margin-top: 5vh !important;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.7);
    border: 1px solid #333;
    border-radius: 8px;

    .el-dialog__header {
        display: none; // 隐藏原始头部，使用自定义头部
    }

    .el-dialog__body {
        position: relative;
        flex: 1;
        display: flex;
        padding: 0;
        height: 100%;
        overflow: hidden;
    }
}

.gallery-container {
    width: 100%;
    display: flex;
    flex-direction: row;
    position: relative;
    height: 100%;
    overflow: hidden;
    background: linear-gradient(to bottom, #121212, #0a0a0a);
}

.gallery-left {
    height: 100%;
    flex: 1;
    display: flex;
    flex-direction: column;
    position: relative;
    transition: all 0.3s ease;

    &.with-sidebar {
        flex: 1;
    }

    // 关闭按钮 - 始终显示在右上角
    .close-btn {
        position: absolute;
        top: 10px;
        right: 10px;
        width: 36px;
        height: 36px;
        border-radius: 50%;
        background: rgba(0, 0, 0, 0.5);
        border: none;
        color: #fff;
        font-size: 18px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.2s;
        z-index: 100; // 提高z-index确保在最上层
        backdrop-filter: blur(3px);

        &:hover {
            background: rgba(255, 70, 70, 0.7);
            transform: scale(1.05);
        }
    }

    .gallery-top {
        flex: 1;
        position: relative;
        width: 100%;
        height: calc(100% - 120px);
        overflow: hidden;

        // 标签切换按钮 - 当标签隐藏时显示
        .toggle-info-btn {
            position: absolute;
            top: 10px;
            left: 10px;
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(0, 0, 0, 0.5);
            border: none;
            color: #fff;
            font-size: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s;
            z-index: 10;
            backdrop-filter: blur(3px);

            &:hover {
                background: rgba(255, 255, 255, 0.25);
                transform: scale(1.05);
            }
        }

        .file-info-bar {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            min-height: 50px;
            max-height: 150px;
            border-radius: 0 0 8px 8px;
            overflow: hidden;
            background: rgba(10, 10, 10, 0.85);
            backdrop-filter: blur(10px);
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 60px 10px 20px;
            z-index: 10;
            transition: opacity 0.3s ease, transform 0.3s ease;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);

            &.hidden {
                opacity: 0;
                transform: translateY(-100%);
                pointer-events: none;
            }

            .file-title {
                color: #fff;
                font-size: 16px;
                max-width: 80%;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }

            .file-actions {
                display: flex;
                gap: 15px;

                .action-btn {
                    background: transparent;
                    border: none;
                    color: #fff;
                    font-size: 18px;
                    cursor: pointer;
                    padding: 5px;
                    border-radius: 4px;
                    transition: all 0.2s;

                    &:hover {
                        background-color: rgba(255, 255, 255, 0.15);
                        transform: scale(1.1);
                    }
                }
            }
        }

        .main-content-area {
            position: relative;
            width: 100%;
            height: calc(100% - 50px);
            margin-top: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 10px;

            .gallery-nav-button {
                position: absolute;
                top: 50%;
                transform: translateY(-50%);
                width: 50px;
                height: 50px;
                border-radius: 50%;
                background: rgba(20, 20, 20, 0.6);
                border: 1px solid rgba(255, 255, 255, 0.1);
                color: #fff;
                font-size: 24px;
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                transition: all 0.3s ease;
                z-index: 10;
                opacity: 0;
                pointer-events: none;
                backdrop-filter: blur(3px);
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);

                &.visible {
                    opacity: 0.8;
                    pointer-events: auto;
                }

                &:hover {
                    opacity: 1;
                    background: rgba(40, 40, 40, 0.8);
                    transform: translateY(-50%) scale(1.05);
                }

                &.disabled {
                    opacity: 0.3;
                    cursor: not-allowed;
                    background: rgba(0, 0, 0, 0.3);
                }

                &.prev {
                    left: 20px;
                }

                &.next {
                    right: 20px;
                }
            }

            .image-container, .video-container, .pdf-container, .dcm-container {
                width: 100%;
                height: 100%;
                border-radius: 8px;
                display: flex;
                align-items: center;
                justify-content: center;
                position: relative;
            }

            .image-container {
                cursor: grab;
                overflow: hidden;
                position: relative;

                .preview {
                    max-width: 90%;
                    max-height: 90%;
                    object-fit: contain;
                    box-shadow: 0 5px 25px rgba(0, 0, 0, 0.5);
                    border-radius: 4px;
                    transform-origin: center;
                    will-change: transform;
                    transition: transform 0.2s ease;

                    &:active {
                        cursor: grabbing;
                    }
                }

                .reset-zoom-btn {
                    position: absolute;
                    bottom: 20px;
                    right: 20px;
                    width: 40px;
                    height: 40px;
                    border-radius: 50%;
                    background: rgba(20, 20, 20, 0.7);
                    border: 1px solid rgba(255, 255, 255, 0.1);
                    color: #fff;
                    font-size: 18px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    cursor: pointer;
                    transition: all 0.2s;
                    z-index: 10;
                    backdrop-filter: blur(3px);

                    &:hover {
                        background: rgba(40, 40, 40, 0.8);
                        transform: scale(1.05);
                    }
                }

                // Add a subtle hint for double-click functionality
                &:after {
                    content: '';
                    position: absolute;
                    top: 10px;
                    left: 10px;
                    width: 30px;
                    height: 30px;
                    background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white" opacity="0.5"><path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/></svg>') no-repeat center center;
                    background-size: contain;
                    opacity: 0;
                    transition: opacity 0.3s ease;
                    pointer-events: none;
                }

                // &:hover:after {
                //     opacity: 0.7;
                // }

                .image-tag {
                    color: #fff;
                    position: absolute;
                    bottom: 6px;
                    left: 6px;
                    z-index: 9;
                    background: var(--tag-bg-color, #0030c6);
                    line-height: 1.5;
                    padding: 2px 6px;
                    border-radius: 4px;
                    font-size: 12px;
                    max-width: 80%;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
                }
            }

            .video-container {
                .main_video {
                    max-width: 90%;
                    max-height: 90%;
                    object-fit: contain;
                    box-shadow: 0 5px 25px rgba(0, 0, 0, 0.5);
                    border-radius: 4px;
                }
            }

            .dcm-container {
                position: relative;

                .preview {
                    max-width: 90%;
                    max-height: 90%;
                    object-fit: contain;
                    box-shadow: 0 5px 25px rgba(0, 0, 0, 0.5);
                    border-radius: 4px;
                }

                .dcm-overlay {
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    color: #fff;
                    background: rgba(20, 20, 20, 0.85);
                    backdrop-filter: blur(5px);
                    padding: 25px;
                    border-radius: 8px;
                    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.4);
                    border: 1px solid rgba(255, 255, 255, 0.1);

                    i {
                        font-size: 48px;
                        margin-bottom: 15px;
                    }

                    span {
                        font-size: 18px;
                        margin-bottom: 20px;
                    }

                    .dcm-open-btn {
                        background: linear-gradient(to bottom, #5aafff, #4a9eff);
                        color: white;
                        border: none;
                        padding: 10px 20px;
                        border-radius: 4px;
                        font-size: 14px;
                        font-weight: 500;
                        cursor: pointer;
                        transition: all 0.3s;
                        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);

                        &:hover {
                            background: linear-gradient(to bottom, #6cb8ff, #5aafff);
                            transform: translateY(-2px);
                            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
                        }
                    }
                }
            }

            .pdf-container {
                position: relative;

                .preview {
                    max-width: 90%;
                    max-height: 90%;
                    object-fit: contain;
                    box-shadow: 0 5px 25px rgba(0, 0, 0, 0.5);
                    border-radius: 4px;
                }

                .pdf-overlay {
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    color: #fff;
                    background: rgba(20, 20, 20, 0.8);
                    backdrop-filter: blur(5px);
                    padding: 25px;
                    border-radius: 8px;
                    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.4);
                    border: 1px solid rgba(255, 255, 255, 0.1);

                    i {
                        font-size: 48px;
                        margin-bottom: 15px;
                    }

                    span {
                        font-size: 18px;
                    }
                }
            }
        }
    }

    .thumb_wrap {
        height: 120px;
        padding: 10px 50px;
        position: relative;
        border-radius: 6px;
        background: #0a0a0a;
        border-top: 1px solid rgba(255, 255, 255, 0.1);
        box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.3);
        .thumb_scroll_wrap {
            width: 100%;
            height: 100%;
            user-select: none;

            .thumb_slide {
                position: relative;
                width: 100%;
                height: 100%;
                z-index: 1;

                .thumbnails-container {
                    display: grid;
                    grid-auto-flow: column;
                    grid-auto-columns: 140px;
                    gap: 12px;
                    height: 100%;
                    align-items: center;
                    padding: 0 5px;
                }

                .thumb_item {
                    height: 90px;
                    background: #181818;
                    position: relative;
                    cursor: pointer;
                    border-radius: 6px;
                    overflow: hidden;
                    transition: all 0.25s ease;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    margin: 5px 0;
                    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);

                    // 重置所有可能的边框和轮廓
                    border: 1px solid rgba(255, 255, 255, 0.05);
                    outline: none;

                    &:hover {
                        transform: translateY(-3px) scale(1.02);
                        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.4);
                        border-color: rgba(255, 255, 255, 0.1);
                    }

                    &.current_thumb {
                        position: relative;
                        border: 2px solid #4a9eff;
                        box-shadow: 0 0 15px rgba(74, 158, 255, 0.4);
                        transform: translateY(-3px) scale(1.05);
                    }

                    .preview {
                        width: 100%;
                        height: 100%;
                        object-fit: contain;
                        position: relative;
                        z-index: 1;
                        // 重置图片可能的边框和轮廓
                        border: none;
                        outline: none;
                        box-shadow: none;
                        // 确保图片填满容器
                        display: block;
                        margin: 0;
                        padding: 0;
                        transition: transform 0.3s ease;
                    }

                    &:hover .preview {
                        transform: scale(1.05);
                    }

                    .file-type-indicator {
                        position: absolute;
                        bottom: 5px;
                        right: 5px;
                        width: 26px;
                        height: 26px;
                        background: rgba(0, 0, 0, 0.7);
                        border-radius: 50%;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        color: #fff;
                        z-index: 6;
                        border: 1px solid rgba(255, 255, 255, 0.1);
                        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);

                        &.video {
                            background: rgba(255, 60, 60, 0.7);
                        }

                        &.pdf {
                            background: rgba(30, 120, 255, 0.7);
                        }

                        &.dcm {
                            background: rgba(30, 200, 130, 0.7);
                        }

                        i {
                            font-size: 14px;
                        }
                    }
                }

                .__bar-is-horizontal,
                .__bar-is-vertical {
                    display: none;
                }
            }
        }

        .nav-button {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(30, 30, 30, 0.7);
            border: 1px solid rgba(255, 255, 255, 0.1);
            color: #fff;
            font-size: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s;
            z-index: 20;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);

            &:hover {
                background: rgba(50, 50, 50, 0.8);
                transform: translateY(-50%) scale(1.05);
            }

            &.prev {
                left: 10px;
            }

            &.next {
                right: 10px;
            }
        }
    }
}

// 右侧插槽样式
.gallery-right {
    height: 100%;
    background: #1a1a1a;
    border-left: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    flex-direction: column;
    transition: all 0.3s ease;
    box-shadow: -2px 0 10px rgba(0, 0, 0, 0.3);

    .sidebar-header {
        height: 50px;
        padding: 0 15px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        background: #0f0f0f;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);

        .sidebar-title {
            color: #fff;
            font-size: 16px;
            font-weight: 500;
        }

        .sidebar-close-btn {
            background: transparent;
            border: none;
            color: #fff;
            font-size: 18px;
            cursor: pointer;
            padding: 5px;
            border-radius: 4px;
            transition: all 0.2s;

            &:hover {
                background-color: rgba(255, 255, 255, 0.15);
                transform: scale(1.1);
            }
        }
    }

    .sidebar-content {
        flex: 1;
        overflow-y: auto;
        color: #fff;
    }
}

.pdfReader {
    position: absolute !important;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 10;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 5px 25px rgba(0, 0, 0, 0.5);
}
</style>
